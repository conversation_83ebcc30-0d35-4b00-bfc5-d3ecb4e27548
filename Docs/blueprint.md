# **App Name**: ResumeFlow

## Core Features:

- Work Experience Input: Allow users to input their work experience with fields for job title, employer, dates, and responsibilities.
- Education Input: Enable users to add education details, including institution, degree, dates, and relevant coursework.
- Skills Section: Provide a section for users to list their skills with proficiency levels.
- Template Selection: Offer various resume templates that users can select to format their information.
- Skill Rewording: Provide users suggestions on how to better express their skills, using AI as a tool for rewording user submitted text.
- PDF Upload and Editing: Allow users to upload PDF resumes and edit the extracted information using Google Cloud Document AI.
- Resume Generation: Generate a downloadable PDF version of the resume based on the selected template and entered data.

## Style Guidelines:

- Primary color: A professional blue (#29ABE2) to convey trust and competence, as the application assists people with their job search. Blue is widely recognized as a color that promotes confidence and clarity.
- Background color: Light grey (#F0F2F5) to provide a clean and neutral backdrop that is easy on the eyes. Its lightness helps in creating a spacious feel, essential for a tool that deals with a lot of textual information.
- Accent color: Teal (#29E2C3) to draw attention to key actions and interactive elements. This hue variation will add a touch of modernism and energy, which is perfect to motivate the users towards their job-seeking endeavors.
- Use clear and professional fonts like Lato for readability.
- Maintain a clean and structured layout for easy data entry and preview.
- Employ simple and recognizable icons for various sections and actions.
- Use subtle transitions and animations for a smooth user experience.