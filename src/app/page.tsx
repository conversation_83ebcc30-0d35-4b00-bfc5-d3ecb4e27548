'use client';

import React from 'react';
import { MainLayout } from '@/components/layout';
import { <PERSON><PERSON>, Card, CardHeader, CardTitle, CardContent } from '@/components/ui';

export default function Home() {
  return (
    <MainLayout>
      {/* Hero Section */}
      <section className="bg-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-text-primary mb-6">
              Build Your Perfect Resume with{' '}
              <span className="text-primary">ResumeFlow</span>
            </h1>
            <p className="text-xl text-text-secondary max-w-3xl mx-auto mb-8">
              A modern, free resume builder inspired by FlowCV with AI-powered features.
              Create professional resumes, upload existing PDFs for editing, and export
              high-quality documents in minutes.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="primary" size="lg">
                Start Building Free
              </Button>
              <Button variant="outline" size="lg">
                View Templates
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-text-primary mb-4">
              Everything You Need to Create Amazing Resumes
            </h2>
            <p className="text-lg text-text-secondary max-w-2xl mx-auto">
              ResumeFlow combines the best of modern web technology with AI-powered features
              to help you create standout resumes.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <Card hover>
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a1 1 0 01-1-1V9a1 1 0 011-1h1a2 2 0 100-4H4a1 1 0 01-1-1V4a1 1 0 011-1h3a1 1 0 011 1v1z" />
                  </svg>
                </div>
                <CardTitle level={3}>Real-time Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-text-secondary">
                  See your resume come to life as you type. Our real-time preview
                  ensures your resume looks perfect before you download it.
                </p>
              </CardContent>
            </Card>

            {/* Feature 2 */}
            <Card hover>
              <CardHeader>
                <div className="w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center mb-4">
                  <svg className="w-6 h-6 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                </div>
                <CardTitle level={3}>PDF Upload & Edit</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-text-secondary">
                  Upload your existing resume PDF and edit it directly in our app.
                  AI-powered text extraction makes it seamless.
                </p>
              </CardContent>
            </Card>

            {/* Feature 3 */}
            <Card hover>
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <CardTitle level={3}>AI-Powered Enhancement</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-text-secondary">
                  Get intelligent suggestions to improve your resume content.
                  Our AI helps you express your skills more effectively.
                </p>
              </CardContent>
            </Card>

            {/* Feature 4 */}
            <Card hover>
              <CardHeader>
                <div className="w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center mb-4">
                  <svg className="w-6 h-6 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
                <CardTitle level={3}>Free & Open Source</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-text-secondary">
                  Completely free to use with no hidden costs. Clone the repository
                  and deploy your own instance easily.
                </p>
              </CardContent>
            </Card>

            {/* Feature 5 */}
            <Card hover>
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M15 5l2 2" />
                  </svg>
                </div>
                <CardTitle level={3}>Professional Templates</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-text-secondary">
                  Choose from carefully designed templates that are ATS-friendly
                  and optimized for modern hiring practices.
                </p>
              </CardContent>
            </Card>

            {/* Feature 6 */}
            <Card hover>
              <CardHeader>
                <div className="w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center mb-4">
                  <svg className="w-6 h-6 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <CardTitle level={3}>High-Quality Export</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-text-secondary">
                  Export your resume as a high-quality PDF that looks great both
                  on screen and in print. Perfect for any application.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-primary py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Build Your Perfect Resume?
          </h2>
          <p className="text-xl text-white/90 mb-8">
            Join thousands of job seekers who have created amazing resumes with ResumeFlow.
          </p>
          <Button variant="secondary" size="lg">
            Start Building Now - It's Free!
          </Button>
        </div>
      </section>
    </MainLayout>
  );
}
