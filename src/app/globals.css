@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap');

:root {
  /* ResumeFlow Color Palette */
  --primary: #29ABE2;        /* Professional Blue */
  --background: #F0F2F5;     /* Light Grey */
  --accent: #29E2C3;         /* Teal */
  --text-primary: #2D3748;   /* Dark Grey */
  --text-secondary: #718096; /* Medium Grey */
  --white: #FFFFFF;
  --border: #E2E8F0;
  --success: #48BB78;
  --warning: #ED8936;
  --error: #F56565;
}

@theme inline {
  /* Colors */
  --color-primary: var(--primary);
  --color-background: var(--background);
  --color-accent: var(--accent);
  --color-white: var(--white);
  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-border: var(--border);
  --color-success: var(--success);
  --color-warning: var(--warning);
  --color-error: var(--error);

  /* Typography */
  --font-sans: 'Lato', system-ui, -apple-system, sans-serif;
  --font-mono: 'Fira Code', Consolas, monospace;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}

body {
  background: var(--background);
  color: var(--text-primary);
  font-family: var(--font-sans);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
