// ResumeFlow Type Definitions

// =============================================================================
// CORE RESUME TYPES
// =============================================================================

export interface PersonalInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  location: string;
  website?: string;
  linkedin?: string;
  github?: string;
  summary?: string;
}

export interface WorkExperience {
  id: string;
  jobTitle: string;
  company: string;
  location: string;
  startDate: string;
  endDate?: string;
  isCurrentJob: boolean;
  responsibilities: string[];
  achievements?: string[];
}

export interface Education {
  id: string;
  institution: string;
  degree: string;
  fieldOfStudy: string;
  startDate: string;
  endDate?: string;
  isCurrentlyStudying: boolean;
  gpa?: string;
  relevantCoursework?: string[];
  honors?: string[];
}

export interface Skill {
  id: string;
  name: string;
  category: SkillCategory;
  proficiency: SkillProficiency;
  yearsOfExperience?: number;
}

export type SkillCategory = 
  | 'technical'
  | 'language'
  | 'soft'
  | 'certification'
  | 'other';

export type SkillProficiency = 
  | 'beginner'
  | 'intermediate'
  | 'advanced'
  | 'expert';

export interface Project {
  id: string;
  name: string;
  description: string;
  technologies: string[];
  startDate: string;
  endDate?: string;
  isOngoing: boolean;
  url?: string;
  githubUrl?: string;
}

export interface Resume {
  id: string;
  personalInfo: PersonalInfo;
  workExperience: WorkExperience[];
  education: Education[];
  skills: Skill[];
  projects?: Project[];
  templateId: string;
  createdAt: string;
  updatedAt: string;
}

// =============================================================================
// TEMPLATE TYPES
// =============================================================================

export interface TemplateConfig {
  id: string;
  name: string;
  description: string;
  category: TemplateCategory;
  previewImage: string;
  isPremium: boolean;
  colorScheme: ColorScheme;
  layout: LayoutConfig;
}

export type TemplateCategory = 
  | 'professional'
  | 'creative'
  | 'modern'
  | 'classic'
  | 'minimal';

export interface ColorScheme {
  primary: string;
  secondary: string;
  accent: string;
  text: string;
  background: string;
}

export interface LayoutConfig {
  columns: number;
  headerStyle: 'centered' | 'left' | 'right';
  sectionSpacing: 'compact' | 'normal' | 'spacious';
  fontFamily: string;
  fontSize: 'small' | 'medium' | 'large';
}

// =============================================================================
// AI SERVICE TYPES
// =============================================================================

export interface AIFeatureFlags {
  aiEnabled: boolean;
  pdfExtractionEnabled: boolean;
  skillEnhancementEnabled: boolean;
}

export interface PDFExtractionResult {
  success: boolean;
  extractedText: string;
  structuredData?: Partial<Resume>;
  confidence: number;
  errors?: string[];
}

export interface SkillEnhancementSuggestion {
  originalText: string;
  suggestedText: string;
  reason: string;
  confidence: number;
}

export interface SkillEnhancementResult {
  success: boolean;
  suggestions: SkillEnhancementSuggestion[];
  errors?: string[];
}

// =============================================================================
// UI COMPONENT TYPES
// =============================================================================

export interface ButtonProps {
  variant: 'primary' | 'secondary' | 'outline' | 'ghost';
  size: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
}

export interface InputProps {
  label?: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  type?: 'text' | 'email' | 'tel' | 'url' | 'password';
  className?: string;
}

export interface FormFieldProps {
  label: string;
  required?: boolean;
  error?: string;
  children: React.ReactNode;
  className?: string;
}

// =============================================================================
// APPLICATION STATE TYPES
// =============================================================================

export interface AppState {
  currentResume: Resume | null;
  selectedTemplate: TemplateConfig | null;
  isPreviewMode: boolean;
  aiFeatures: AIFeatureFlags;
  isLoading: boolean;
  errors: string[];
}

export interface ResumeFormState {
  personalInfo: PersonalInfo;
  workExperience: WorkExperience[];
  education: Education[];
  skills: Skill[];
  projects: Project[];
  isDirty: boolean;
  validationErrors: Record<string, string>;
}

// =============================================================================
// API TYPES
// =============================================================================

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface FileUploadResponse {
  success: boolean;
  fileId: string;
  fileName: string;
  fileSize: number;
  extractedData?: Partial<Resume>;
  error?: string;
}

// =============================================================================
// UTILITY TYPES
// =============================================================================

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
