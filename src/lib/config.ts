// ResumeFlow Configuration
// Centralized configuration management with environment variables

import { AIFeatureFlags } from '@/types';

// =============================================================================
// ENVIRONMENT VALIDATION
// =============================================================================

function getEnvVar(key: string, defaultValue?: string): string {
  const value = process.env[key] || defaultValue;
  if (!value && !defaultValue) {
    console.warn(`Environment variable ${key} is not set`);
  }
  return value || '';
}

function getBooleanEnvVar(key: string, defaultValue: boolean = false): boolean {
  const value = process.env[key];
  if (!value) return defaultValue;
  return value.toLowerCase() === 'true';
}

// =============================================================================
// APPLICATION CONFIG
// =============================================================================

export const config = {
  // App Settings
  app: {
    name: 'ResumeFlow',
    version: '0.1.0',
    url: getEnvVar('NEXT_PUBLIC_APP_URL', 'http://localhost:3000'),
    environment: getEnvVar('NODE_ENV', 'development'),
  },

  // Feature Flags
  features: {
    aiEnabled: getBooleanEnvVar('AI_FEATURES_ENABLED', false),
    pdfExtractionEnabled: getBooleanEnvVar('PDF_AI_EXTRACTION_ENABLED', false),
    skillEnhancementEnabled: getBooleanEnvVar('SKILL_ENHANCEMENT_ENABLED', false),
    databaseEnabled: getBooleanEnvVar('DATABASE_ENABLED', false),
  } as AIFeatureFlags & { databaseEnabled: boolean },

  // Database (Supabase)
  database: {
    url: getEnvVar('NEXT_PUBLIC_SUPABASE_URL'),
    anonKey: getEnvVar('NEXT_PUBLIC_SUPABASE_ANON_KEY'),
    serviceRoleKey: getEnvVar('SUPABASE_SERVICE_ROLE_KEY'),
  },

  // AI Services
  ai: {
    // Google Cloud Document AI
    googleCloud: {
      projectId: getEnvVar('GOOGLE_CLOUD_PROJECT_ID'),
      privateKey: getEnvVar('GOOGLE_CLOUD_PRIVATE_KEY'),
      clientEmail: getEnvVar('GOOGLE_CLOUD_CLIENT_EMAIL'),
      processorId: getEnvVar('GOOGLE_CLOUD_DOCUMENT_AI_PROCESSOR_ID'),
      location: getEnvVar('GOOGLE_CLOUD_DOCUMENT_AI_LOCATION', 'us'),
    },

    // Gemma Model
    gemma: {
      modelPath: getEnvVar('GEMMA_MODEL_PATH'),
      enabled: getBooleanEnvVar('GEMMA_MODEL_ENABLED', false),
    },
  },

  // Logging
  logging: {
    level: getEnvVar('LOG_LEVEL', 'info'),
  },

  // File Upload
  upload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['application/pdf'],
    uploadDir: '/tmp/uploads',
  },

  // Security
  security: {
    corsOrigins: [
      getEnvVar('NEXT_PUBLIC_APP_URL', 'http://localhost:3000'),
    ],
  },
} as const;

// =============================================================================
// VALIDATION FUNCTIONS
// =============================================================================

export function validateConfig(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Required environment variables
  if (!config.app.url) {
    errors.push('NEXT_PUBLIC_APP_URL is required');
  }

  // AI feature validation
  if (config.features.pdfExtractionEnabled) {
    if (!config.ai.googleCloud.projectId) {
      errors.push('GOOGLE_CLOUD_PROJECT_ID is required when PDF extraction is enabled');
    }
    if (!config.ai.googleCloud.privateKey) {
      errors.push('GOOGLE_CLOUD_PRIVATE_KEY is required when PDF extraction is enabled');
    }
    if (!config.ai.googleCloud.clientEmail) {
      errors.push('GOOGLE_CLOUD_CLIENT_EMAIL is required when PDF extraction is enabled');
    }
  }

  if (config.features.skillEnhancementEnabled) {
    if (!config.ai.gemma.modelPath) {
      errors.push('GEMMA_MODEL_PATH is required when skill enhancement is enabled');
    }
  }

  // Database validation
  if (config.features.databaseEnabled) {
    if (!config.database.url) {
      errors.push('NEXT_PUBLIC_SUPABASE_URL is required when database is enabled');
    }
    if (!config.database.anonKey) {
      errors.push('NEXT_PUBLIC_SUPABASE_ANON_KEY is required when database is enabled');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// =============================================================================
// FEATURE DETECTION
// =============================================================================

export function getAvailableFeatures(): AIFeatureFlags & { databaseEnabled: boolean } {
  const validation = validateConfig();
  
  return {
    aiEnabled: config.features.aiEnabled && validation.isValid,
    pdfExtractionEnabled: config.features.pdfExtractionEnabled && 
                         !!config.ai.googleCloud.projectId &&
                         !!config.ai.googleCloud.privateKey,
    skillEnhancementEnabled: config.features.skillEnhancementEnabled &&
                            !!config.ai.gemma.modelPath,
    databaseEnabled: config.features.databaseEnabled &&
                    !!config.database.url &&
                    !!config.database.anonKey,
  };
}

// =============================================================================
// DEVELOPMENT HELPERS
// =============================================================================

export function isDevelopment(): boolean {
  return config.app.environment === 'development';
}

export function isProduction(): boolean {
  return config.app.environment === 'production';
}

export function logConfig(): void {
  if (isDevelopment()) {
    console.log('🔧 ResumeFlow Configuration:', {
      app: config.app,
      features: getAvailableFeatures(),
      environment: config.app.environment,
    });
  }
}

// =============================================================================
// EXPORT DEFAULT
// =============================================================================

export default config;
