// Card Component - ResumeFlow UI Library
'use client';

import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  shadow?: 'none' | 'sm' | 'md' | 'lg';
  border?: boolean;
  hover?: boolean;
}

const Card: React.FC<CardProps> = ({
  children,
  className = '',
  padding = 'md',
  shadow = 'sm',
  border = true,
  hover = false,
}) => {
  // Base card styles
  const baseStyles = `
    bg-white rounded-lg
    transition-all duration-200 ease-in-out
  `;

  // Padding styles
  const paddingStyles = {
    none: '',
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6',
  };

  // Shadow styles
  const shadowStyles = {
    none: '',
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg',
  };

  // Border styles
  const borderStyles = border ? 'border border-border' : '';

  // Hover styles
  const hoverStyles = hover ? 'hover:shadow-md hover:scale-[1.02]' : '';

  // Combine all styles
  const combinedStyles = `
    ${baseStyles}
    ${paddingStyles[padding]}
    ${shadowStyles[shadow]}
    ${borderStyles}
    ${hoverStyles}
    ${className}
  `.replace(/\s+/g, ' ').trim();

  return (
    <div className={combinedStyles}>
      {children}
    </div>
  );
};

// Card Header Component
interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

export const CardHeader: React.FC<CardHeaderProps> = ({
  children,
  className = '',
}) => {
  const headerStyles = `
    border-b border-border pb-3 mb-4
    ${className}
  `.replace(/\s+/g, ' ').trim();

  return (
    <div className={headerStyles}>
      {children}
    </div>
  );
};

// Card Title Component
interface CardTitleProps {
  children: React.ReactNode;
  className?: string;
  level?: 1 | 2 | 3 | 4 | 5 | 6;
}

export const CardTitle: React.FC<CardTitleProps> = ({
  children,
  className = '',
  level = 2,
}) => {
  const titleStyles = `
    font-semibold text-text-primary
    ${level === 1 ? 'text-2xl' : ''}
    ${level === 2 ? 'text-xl' : ''}
    ${level === 3 ? 'text-lg' : ''}
    ${level === 4 ? 'text-base' : ''}
    ${level === 5 ? 'text-sm' : ''}
    ${level === 6 ? 'text-xs' : ''}
    ${className}
  `.replace(/\s+/g, ' ').trim();

  const Tag = `h${level}` as keyof JSX.IntrinsicElements;

  return (
    <Tag className={titleStyles}>
      {children}
    </Tag>
  );
};

// Card Content Component
interface CardContentProps {
  children: React.ReactNode;
  className?: string;
}

export const CardContent: React.FC<CardContentProps> = ({
  children,
  className = '',
}) => {
  const contentStyles = `
    text-text-primary
    ${className}
  `.replace(/\s+/g, ' ').trim();

  return (
    <div className={contentStyles}>
      {children}
    </div>
  );
};

// Card Footer Component
interface CardFooterProps {
  children: React.ReactNode;
  className?: string;
}

export const CardFooter: React.FC<CardFooterProps> = ({
  children,
  className = '',
}) => {
  const footerStyles = `
    border-t border-border pt-3 mt-4
    ${className}
  `.replace(/\s+/g, ' ').trim();

  return (
    <div className={footerStyles}>
      {children}
    </div>
  );
};

export default Card;
