// Input Component Tests
import { render, screen, fireEvent } from '@/utils/test-utils';
import Input from '../Input';

describe('Input Component', () => {
  it('renders with basic props', () => {
    const handleChange = jest.fn();
    render(<Input value="" onChange={handleChange} placeholder="Enter text" />);
    
    const input = screen.getByPlaceholderText('Enter text');
    expect(input).toBeInTheDocument();
    expect(input).toHaveAttribute('type', 'text');
  });

  it('renders with label', () => {
    const handleChange = jest.fn();
    render(<Input label="Email" value="" onChange={handleChange} />);
    
    expect(screen.getByLabelText('Email')).toBeInTheDocument();
    expect(screen.getByText('Email')).toBeInTheDocument();
  });

  it('shows required indicator when required', () => {
    const handleChange = jest.fn();
    render(<Input label="Required Field" value="" onChange={handleChange} required />);
    
    const label = screen.getByText('Required Field');
    expect(label).toBeInTheDocument();
    // The asterisk is added via CSS pseudo-element, so we check the class
    expect(label).toHaveClass("after:content-['*']");
  });

  it('handles value changes', () => {
    const handleChange = jest.fn();
    render(<Input value="" onChange={handleChange} />);
    
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'test value' } });
    
    expect(handleChange).toHaveBeenCalledWith('test value');
  });

  it('shows error state', () => {
    const handleChange = jest.fn();
    render(<Input value="" onChange={handleChange} error="This field is required" />);
    
    const input = screen.getByRole('textbox');
    const errorMessage = screen.getByText('This field is required');
    
    expect(input).toHaveClass('border-error');
    expect(input).toHaveAttribute('aria-invalid', 'true');
    expect(errorMessage).toBeInTheDocument();
    expect(errorMessage).toHaveAttribute('role', 'alert');
  });

  it('renders different input types', () => {
    const handleChange = jest.fn();
    const { rerender } = render(<Input type="email" value="" onChange={handleChange} />);
    expect(screen.getByRole('textbox')).toHaveAttribute('type', 'email');

    rerender(<Input type="tel" value="" onChange={handleChange} />);
    expect(screen.getByRole('textbox')).toHaveAttribute('type', 'tel');

    rerender(<Input type="url" value="" onChange={handleChange} />);
    expect(screen.getByRole('textbox')).toHaveAttribute('type', 'url');
  });

  it('is disabled when disabled prop is true', () => {
    const handleChange = jest.fn();
    render(<Input value="" onChange={handleChange} disabled />);
    
    const input = screen.getByRole('textbox');
    expect(input).toBeDisabled();
    expect(input).toHaveClass('disabled:opacity-50');
  });

  it('applies custom className', () => {
    const handleChange = jest.fn();
    render(<Input value="" onChange={handleChange} className="custom-input" />);
    
    expect(screen.getByRole('textbox')).toHaveClass('custom-input');
  });

  it('associates label with input correctly', () => {
    const handleChange = jest.fn();
    render(<Input label="Test Label" value="" onChange={handleChange} />);
    
    const input = screen.getByRole('textbox');
    const label = screen.getByText('Test Label');
    
    expect(input).toHaveAttribute('id');
    expect(label).toHaveAttribute('for', input.getAttribute('id'));
  });
});
