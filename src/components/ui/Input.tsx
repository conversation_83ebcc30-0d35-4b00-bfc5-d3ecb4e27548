// Input Component - ResumeFlow UI Library
'use client';

import React from 'react';
import { InputProps } from '@/types';

const Input: React.FC<InputProps> = ({
  label,
  placeholder,
  value,
  onChange,
  error,
  disabled = false,
  required = false,
  type = 'text',
  className = '',
}) => {
  const inputId = React.useId();

  // Base input styles
  const baseInputStyles = `
    w-full px-3 py-2 border rounded-md
    font-sans text-base text-text-primary
    placeholder:text-text-secondary
    transition-all duration-200 ease-in-out
    focus:outline-none focus:ring-2 focus:ring-offset-1
    disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-background
  `;

  // Input state styles
  const stateStyles = error
    ? 'border-error focus:border-error focus:ring-error/50'
    : 'border-border focus:border-primary focus:ring-primary/50';

  // Label styles
  const labelStyles = `
    block text-sm font-medium text-text-primary mb-1
    ${required ? "after:content-['*'] after:text-error after:ml-1" : ''}
  `;

  // Error message styles
  const errorStyles = 'mt-1 text-sm text-error';

  // Combine input styles
  const combinedInputStyles = `
    ${baseInputStyles}
    ${stateStyles}
    ${className}
  `.replace(/\s+/g, ' ').trim();

  return (
    <div className="w-full">
      {label && (
        <label htmlFor={inputId} className={labelStyles}>
          {label}
        </label>
      )}
      
      <input
        id={inputId}
        type={type}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
        required={required}
        className={combinedInputStyles}
        aria-invalid={!!error}
        aria-describedby={error ? `${inputId}-error` : undefined}
      />
      
      {error && (
        <p id={`${inputId}-error`} className={errorStyles} role="alert">
          {error}
        </p>
      )}
    </div>
  );
};

export default Input;
